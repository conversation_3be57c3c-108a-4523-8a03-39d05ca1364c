[tox]
envlist = py313,lint,precommit,test

[flake8]
exclude = repository_service_tuf_worker/__init__.py,venv,.venv,settings.py,.git,.tox,dist,docs,*lib/python*,*egg,build,tools

[testenv]
setenv =
    RSTUF_WORKER_ID = "test"
    RSTUF_BROKER_SERVER = fakeserver
    RSTUF_REDIS_SERVER = redis://fake-redis
    RSTUF_DB_SERVER = postgresql://fake-sql
    RSTUF_STORAGE_BACKEND = LocalStorage
    RSTUF_LOCAL_STORAGE_BACKEND_PATH = ./data-test/s
    DATA_DIR = ./data-test

deps = pipenv
commands_pre =
    pipenv install --dev
    pipenv install

[testenv:lint]
deps = 
    pre-commit
    pipenv
setenv =
    BLACK_CACHE_DIR = ~/.cache/black/
commands =
    pre-commit run flake8 --all-files --show-diff-on-failure
    pre-commit run isort --all-files --show-diff-on-failure
    pre-commit run black --all-files --show-diff-on-failure
    pre-commit run bandit --all-files --show-diff-on-failure

[testenv:test]
commands =
    python -m pytest --cov-report=xml --cov-report=term --cov-config=tox.ini --cov -n auto -vv tests/

[run]
omit = tests/*

[testenv:precommit]
description="Check if `pre-commit autoupdate` is up-to-date."
deps =
    pre-commit
    pipenv
skipsdist=false
allowlist_externals =
    bash
commands =
    pre-commit --version
    bash -c 'cp .pre-commit-config.yaml tmp-tox-precommit.yaml'
    bash -c 'pre-commit autoupdate'
    bash -c 'if ! diff -w .pre-commit-config.yaml tmp-tox-precommit.yaml; \
            then echo "⚠️ [WARNING] pre-commit hooks are outdated. To update them run: pre-commit autoupdate"; fi'
    bash -c 'cp tmp-tox-precommit.yaml .pre-commit-config.yaml'
    bash -c 'rm tmp-tox-precommit.yaml'

[testenv:docs]
deps = pipenv
commands_pre =
    pipenv install --dev
    pipenv install

allowlist_externals =
    plantuml
commands =
    plantuml -Djava.awt.headless=true -o ../source/_static/ -tpng docs/diagrams/*.puml
	sphinx-apidoc -f -o docs/source/devel/ repository_service_tuf_worker
	sphinx-build -E -W -b html docs/source docs/build/html


[gh-actions]
python =
    3.13: py313,pep8,lint,precommit,test

[testenv:local-aws-kms]
deps =
    pipenv
    localstack

allowlist_externals =
    localstack
    bash

setenv =
    DATA_DIR = ./data-test
    RSTUF_AWS_ACCESS_KEY_ID = test
    RSTUF_AWS_SECRET_ACCESS_KEY = test
    RSTUF_AWS_ENDPOINT_URL = http://localhost:4566/
    RSTUF_AWS_DEFAULT_REGION = us-east-1

commands_pre =
    # Start virtual AWS KMS
    localstack start --detached
    localstack wait

    # Create signing key
    bash {toxinidir}/tests/files/aws/init-kms.sh

commands =
    python3 -m pytest tests/unit/tuf_repository_service_worker/test_signer.py -k test_get_from_aws

commands_post =
    # Stop virtual AWS KMS
    localstack stop
