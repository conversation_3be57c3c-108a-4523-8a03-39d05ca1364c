=================================================
Contributing to Repository Service for TUF WORKER
=================================================

We welcome contributions from the community and first want to thank you for
taking the time to contribute!

Please familiarize yourself with the `Code of Conduct`_
before contributing.

DCO
===

Before you start working with Repository Service for TUF, please read our
`Developer Certificate of Origin <https://cla.vmware.com/dco>`_.
All contributions to this repository must be signed as described on that page.

To acknowledge the Developer Certificate of Origin (DCO), sign your commits
by appending a ``Signed-off-by:
Your Name <<EMAIL>>`` to each git commit message (see `git commit
--signoff <https://git-scm.com/docs/git-commit#Documentation/git-commit.txt---signoff>`_).
Your signature certifies that you wrote the patch or have the right to pass it
on as an open-source patch.

Getting started
===============

We welcome many different types of contributions and not all of them need a
Pull Request. Contributions may include:

* New features and proposals
* Documentation
* Bug fixes
* Issue Triage
* Answering questions and giving feedback
* Helping to onboard new contributors
* Other related activities


Check `README.rst <README.rst>`_ development section instructions.