{"settings": {"roles": {"root": {"expiration": 365}, "timestamp": {"expiration": 1}, "snapshot": {"expiration": 1}, "targets": {"expiration": 365}, "bins": {"expiration": 1, "number_of_delegated_bins": 2}, "delegations": null}}, "metadata": {"root": {"signatures": [{"keyid": "50d7e110ad65f3b2dba5c3cfc8c5ca259be9774cc26be3410044ffd4be3aa5f3", "sig": "3044022079c714e5fec32535dfc6460d6fce35b53e89987cd247be593f5bcb4ecb5a8c6b02205e850ab7d238f749e33222a1cc7b92986948651083a43b524caca2fad846568d"}], "signed": {"_type": "root", "version": 1, "spec_version": "1.0.31", "expires": "2026-07-06T08:17:46Z", "consistent_snapshot": true, "keys": {"50d7e110ad65f3b2dba5c3cfc8c5ca259be9774cc26be3410044ffd4be3aa5f3": {"keytype": "ecdsa", "scheme": "ecdsa-sha2-nistp256", "keyval": {"public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFk<PERSON>EwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEcLYSZyFGeKdWNt5dWFbnv6N9NyHC\noUNLcG6GZIxLwN8Q8MUdHdOOxGkDnyBRSJpIZ/r/oDECSTwfCYhdogweLA==\n-----END PUBLIC KEY-----\n"}, "x-rstuf-key-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "c6d8bf2e4f48b41ac2ce8eca21415ca8ef68c133b47fc33df03d4070a7e1e9cc": {"keytype": "ed25519", "scheme": "ed25519", "keyval": {"public": "4f66dabebcf30628963786001984c0b75c175cdcf3bc4855933a2628f0cd0a0f"}, "x-rstuf-key-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "2f685fa7546f1856b123223ab086b3def14c89d24eef18f49c32508c2f60e241": {"keytype": "rsa", "scheme": "rsassa-pss-sha256", "keyval": {"public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwhX6rioiL/cX5Ys32InF\nU52H8tL14QeX0tacZdb+AwcH6nIh97h3RSHvGD7Xy6uaMRmGldAnSVYwJHqoJ5j2\nynVzU/RFpr+6n8Ps0QFg5GmlEqZboFjLbS0bsRQcXXnqJNsVLEPT3ULvu1rFRbWz\nAMFjNtNNk5W/u0GEzXn3D03jIdhD8IKAdrTRf0VMD9TRCXLdMmEU2vkf1NVUnOTb\n/dRX5QA8TtBylVnouZknbavQ0J/pPlHLfxUgsKzodwDlJmbPG9BWwXqQCmP0DgOG\nNIZ1X281MOBaGbkNVEuntNjCSaQxQjfALVVU5NAfal2cwMINtqaoc7Wa+TWvpFEI\nWwIDAQAB\n-----END PUBLIC KEY-----\n"}, "x-rstuf-key-name": "<PERSON><PERSON><PERSON><PERSON>"}, "0d9d3d4bad91c455bc03921daa95774576b86625ac45570d0cac025b08e65043": {"keytype": "rsa", "scheme": "rsassa-pss-sha256", "keyval": {"public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwJNtmJy0bky0VZHhJoVw\nVR0oIto8ndLLicnaHPDUfFsv2dTP50uLiYuYhU/RLTh+PIMm9dU5gvfSQ0YIUFHO\nfdcDCBaMNYR9z9c9kvWkfgxP4H7cMdy9ev3yh4pL+ua64AT1598QxmF0RSp9p8P4\nUDPJC4XsgVz3kKeCSQAgz02MJ+KdHyTDP+rgOuWQfVL8bz53puMSSFojiaEJTmZQ\n7eBnI2n6UF6AAV6eo6Dc4cgPQLSjhDqcfoHCyk/AzpTQO5EV+ahofYmV/kQQtr7g\nz8MQXoKRwCbfIcWhPyfPNReOo7fqVK3uK3kkD1ouoNSr9DFRcnUbsX4QR/CQLcoP\nXwIDAQAB\n-----END PUBLIC KEY-----\n"}, "x-rstuf-key-name": "online1", "x-rstuf-online-key-uri": "fn:0d9d3d4bad91c455bc03921daa95774576b86625ac45570d0cac025b08e65043"}}, "roles": {"timestamp": {"keyids": ["0d9d3d4bad91c455bc03921daa95774576b86625ac45570d0cac025b08e65043"], "threshold": 1}, "snapshot": {"keyids": ["0d9d3d4bad91c455bc03921daa95774576b86625ac45570d0cac025b08e65043"], "threshold": 1}, "root": {"keyids": ["50d7e110ad65f3b2dba5c3cfc8c5ca259be9774cc26be3410044ffd4be3aa5f3", "c6d8bf2e4f48b41ac2ce8eca21415ca8ef68c133b47fc33df03d4070a7e1e9cc", "2f685fa7546f1856b123223ab086b3def14c89d24eef18f49c32508c2f60e241"], "threshold": 2}, "targets": {"keyids": ["0d9d3d4bad91c455bc03921daa95774576b86625ac45570d0cac025b08e65043"], "threshold": 1}}}}}, "timeout": 300}