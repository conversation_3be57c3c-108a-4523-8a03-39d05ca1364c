[program:rstuf_worker]
command = celery -A app worker -l info -Q metadata_repository -n rstuf@%(ENV_RSTUF_WORKER_ID)s
directory = /opt/repository-service-tuf-worker
startsecs = 5
autostart = true
autorestart = true
stopwaitsecs = 300
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes = 0
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes = 0

[program:rstuf_worker_jobs]
command = celery -A app worker -B -l info -Q rstuf_internals -n rstuf_jobs@%(ENV_RSTUF_WORKER_ID)s
directory = /opt/repository-service-tuf-worker
startsecs = 5
autostart = true
autorestart = true
stopwaitsecs = 300
stderr_logfile = /dev/stderr
stderr_logfile_maxbytes = 0
stdout_logfile = /dev/stdout
stdout_logfile_maxbytes = 0

[supervisord]
loglevel = info
nodaemon = true
pidfile = /tmp/supervisord.pid
logfile = /dev/null
logfile_maxbytes = 0