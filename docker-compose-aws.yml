# Development deployment using AWS RSTUF Services
version: "3.9"

volumes:
  repository-service-tuf-storage:
  repository-service-tuf-api-data:
  repository-service-tuf-redis-data:
  repository-service-tuf-pgsql-data:

services:
  postgres:
    image: postgres:17.5-alpine3.21
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_PASSWORD=secret
    volumes:
      - "repository-service-tuf-pgsql-data:/var/lib/postgresql/data"
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres", "-d", "postgres"]
      interval: 1s

  repository-service-tuf-api:
    image: ghcr.io/repository-service-tuf/repository-service-tuf-api:${API_VERSION}
    volumes:
      - repository-service-tuf-api-data:/data
    ports:
      - 80:80
    environment:
      - RSTUF_BROKER_SERVER=redis://redis
      - RSTUF_REDIS_SERVER=redis://redis
    depends_on:
      redis:
        condition: service_healthy

  redis:
    image: redis:8.0.0-alpine3.21
    volumes:
      - repository-service-tuf-redis-data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 1s

  localstack:
    image: localstack/localstack:2.2
    environment:
      HOSTNAME: "localstack"
      LOCALSTACK_HOST: "localstack"
      LS_LOG: "error"
    ports:
      - "4566:4566"
    volumes:
      - "./tests/files/aws/init-services.sh:/etc/localstack/init/ready.d/init-services.sh"

  repository-service-tuf-worker:
    build:
      context: .
    entrypoint: "bash entrypoint-dev.sh"
    environment:
      - DATA_DIR=./data
      - RSTUF_STORAGE_BACKEND=AWSS3
      - RSTUF_AWS_STORAGE_BUCKET=tuf-metadata
      - RSTUF_AWS_ACCESS_KEY_ID=access_key
      - RSTUF_AWS_SECRET_ACCESS_KEY=secret_key
      # region and endpoint_url are required by localstack
      - RSTUF_AWS_DEFAULT_REGION=us-east-1
      - RSTUF_AWS_ENDPOINT_URL=http://localstack:4566
      - RSTUF_ONLINE_KEY_DIR=/var/opt/repository-service-tuf/key_storage
      - RSTUF_BROKER_SERVER=redis://redis
      - RSTUF_REDIS_SERVER=redis://redis
      - RSTUF_DB_SERVER=******************************************
      - METADATA_BASE_URL="http://localstack:4566/tuf-metadata/"
    volumes:
      - ./:/opt/repository-service-tuf-worker:z
      - ./tests/files/key_storage/:/var/opt/repository-service-tuf/key_storage
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      localstack:
        condition: service_healthy
    tty: true
    stdin_open: true

  rstuf-ft-runner:
    image: python:3.13-slim
    command: python -V
    working_dir: /rstuf-runner
    volumes:
      - ./:/rstuf-runner