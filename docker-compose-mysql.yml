# Development deployment using Redis as Broker
version: "3.9"

volumes:
  repository-service-tuf-storage:
  repository-service-tuf-api-data:
  repository-service-tuf-redis-data:
  repository-service-tuf-mysql-data:

services:
  mysql:
    image: mysql:oracle
    restart: always
    ports:
      - 3306:3306
    environment:
      MYSQL_DATABASE: rstuf
      MYSQL_ROOT_PASSWORD: secret
    volumes:
      - repository-service-tuf-mysql-data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-psecret"]

  repository-service-tuf-api:
    image: ghcr.io/repository-service-tuf/repository-service-tuf-api:${API_VERSION}
    volumes:
      - repository-service-tuf-api-data:/data
    ports:
      - 80:80
    environment:
      - RSTUF_BROKER_SERVER=redis://redis
      - RSTUF_REDIS_SERVER=redis://redis
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy

  web:
    image: python:3.13-slim
    command: python -m http.server -d /var/opt/repository-service-tuf/storage 8080
    volumes:
      - repository-service-tuf-storage:/var/opt/repository-service-tuf/storage
    ports:
      - "8080:8080"

  redis:
    image: redis:8.0.0-alpine3.21
    volumes:
      - repository-service-tuf-redis-data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 1s

  repository-service-tuf-worker:
    build:
      context: .
    entrypoint: "bash entrypoint-dev.sh"
    environment:
      - DATA_DIR=./data
      - RSTUF_STORAGE_BACKEND=LocalStorage
      - RSTUF_LOCAL_STORAGE_BACKEND_PATH=/var/opt/repository-service-tuf/storage
      - RSTUF_ONLINE_KEY_DIR=/var/opt/repository-service-tuf/key_storage
      - RSTUF_BROKER_SERVER=redis://redis
      - RSTUF_REDIS_SERVER=redis://redis
      - RSTUF_SQL_SERVER=mysql+pymysql://root:secret@mysql:3306/rstuf
    volumes:
      - ./:/opt/repository-service-tuf-worker:z
      - repository-service-tuf-storage:/var/opt/repository-service-tuf/storage
      - ./tests/files/key_storage/:/var/opt/repository-service-tuf/key_storage
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
    tty: true
    stdin_open: true

  rstuf-ft-runner:
    image: python:3.13-slim
    command: python -V
    working_dir: /rstuf-runner
    environment:
      - PERFORMANCE=false
    volumes:
      - ./:/rstuf-runner