@startuml repository-service-tuf-worker-C2
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Services/web_services>
!include <office/Servers/database_server>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>
!include <cloudinsight/redis>

!include <azure/AzureCommon>
!include <azure/Storage/AzureBlobStorage>
!include <azure/Security/AzureKeyVault>

!include <awslib/AWSCommon>
!include <awslib/SecurityIdentityCompliance/KeyManagementService>
!include <awslib/Storage/SimpleStorageService>
!include https://raw.githubusercontent.com/repository-service-tuf/repository-service-tuf/main/docs/diagrams/rstuf_icon.puml


AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("key_service", $sprite="lock_with_key_security", $legendText="Key Service", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("repository_service_tuf_worker", $sprite="rstuf", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("sql_db", $sprite="database_server", $legendText="SQL Server")
AddContainerTag("download_artifact", $sprite="download", $legendText="Download file/artifact/package", $bgColor=Gray)
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")
AddContainerTag("redis", $sprite="redis", $legendText="CLI")
AddContainerTag("aws_s3", $sprite="S3Bucket", $legendText="Storage")
AddContainerTag("aws_kms", $sprite="KeyManagementService", $legendText="KeyVault")
AddContainerTag("azure_blob", $sprite="AzureBlobStorage", $legendText="Storge")
AddContainerTag("azure_kv", $sprite="AzureKeyVault", $legendText="KeyVault")


Container(repository_service_tuf_worker, "REPOSITORY-SERVICE-TUF-WORKER", "Metadata Repository Worker", $tags="repository_service_tuf_worker") #DodgerBlue
ContainerDb(data_dir, "/$DATA_DIR", "File System", "Service settings", $tags="storage_service")
Container_Ext(broker, "Broker/Backend", "RabbitMQ, Redis, etc", $tags="queue") #Grey
Container_Ext(redis, "Redis", "Redis Server", $tags="redis") #Grey
Container_Ext(sql, "PostgreSQL", "SQL Server", $tags="sql_db") #Grey
Container_Boundary(repository_storage, "Storage Service"){
    Container_Ext(stg_fs, "Filesystem", "Storage", $tags="storage_service") #Grey
    Container_Ext(aws_s3, "AWS S3", "Storage", $tags="aws_s3") #Grey
    Container_Ext(azure_blob, "Azure Blob", "Storage", $tags="azure_blob") #Grey
}
Rel_D(broker, repository_service_tuf_worker, "Consumer", "Tasks")
Rel_U(repository_service_tuf_worker, broker, "Publisher", "Tasks Results")
BiRel_U(repository_service_tuf_worker, redis, "Repository Configuration", "Consumer")
BiRel_U(repository_service_tuf_worker, sql, "TargetsFiles, TargetsRoles", "")
BiRel_R(repository_service_tuf_worker, data_dir, "Write/Read", "Service settings")
BiRel_D(repository_service_tuf_worker, repository_storage, "Write/Read", "TUF Metadata")



HIDE_STEREOTYPE()
@enduml