@startuml repository-service-tuf-worker-C1
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml
!include https://raw.githubusercontent.com/repository-service-tuf/repository-service-tuf/main/docs/diagrams/rstuf_icon.puml

AddRelTag("validate", $textColor="orange", $lineColor="Blue", $lineStyle = DashedLine())
AddRelTag("download", $textColor="orange", $lineColor="Blue")

System(queue, "Broker/Backend", "Redis, RabbitMQ, etc") #Grey
System(repository_service_tuf_worker, "Repository Worker", "repository-service-tuf-worker", $sprite="rstuf")
System_Ext(repository_storage, "Storage Service", "File System, Object Storage, Database, etc")
System_Ext(key_storage, "Key Vault Service", "File System, AWS KMS, Azure Key Vault, etc")
Rel_D(queue, repository_service_tuf_worker, "Consumer")
Rel_U(repository_service_tuf_worker, queue, "Publisher")
Rel_D(repository_service_tuf_worker, repository_storage, "Read/Write")
Rel_D(repository_service_tuf_worker, key_storage, "Read/Write")

HIDE_STEREOTYPE()
@enduml