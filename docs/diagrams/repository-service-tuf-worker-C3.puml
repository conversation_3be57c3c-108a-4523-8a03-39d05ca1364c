@startuml repository-service-tuf-worker-C3
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

!define osaPuml https://raw.githubusercontent.com/Crashedmind/PlantUML-opensecurityarchitecture2-icons/master
!include osaPuml/Common.puml
!include osaPuml/User/all.puml

!include <office/Servers/file_server>
!include <office/Servers/application_server>
!include <office/Servers/database_server>
!include <office/Services/web_services>
!include <office/Concepts/application_generic>
!include <office/Concepts/service_application>
!include <office/Servers/web_server>
!include <office/Communications/queue_viewer>
!include <office/Devices/management_console>
!include <logos/terminal>
!include <office/Security/lock_with_key_security>
!include <office/Concepts/download>


AddContainerTag("webui", $sprite="application_server", $legendText="Web UI Interface", $bgColor=Gray)
AddContainerTag("storage_service", $sprite="file_server", $legendText="Storage Service", $bgColor=Gray)
AddContainerTag("rest_api", $sprite="web_server", $legendText="Repository REST API")
AddContainerTag("repository_service_tuf_worker", $sprite="service_application", $legendText="Repository Metadata Worker")
AddContainerTag("queue", $sprite="queue_viewer", $legendText="Message Queue")
AddContainerTag("sql_db", $sprite="database_server", $legendText="SQL Server")
AddContainerTag("ci_cd", $sprite="application_generic", $legendText="CI/CD, Artfact Management, etc")
AddContainerTag("metadata_web", $sprite="web_services", $legendText="Web exposed TUF Metadata")
AddContainerTag("download_artifact", $sprite="download", $legendText="Download file/artifact/package", $bgColor=Gray)
AddContainerTag("terminal", $sprite="terminal,scale=0.5,color=#000000", $legendText="CLI")

AddRelTag("terminal", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="terminal,scale=0.75,color=#000000", $legendText="Repository Service for TUF CLI")
AddRelTag("bowser", $textColor="$ARROW_COLOR", $lineColor="$ARROW_COLOR", $sprite="management_console,scale=0.5", $legendText="Browser")
AddRelTag("download", $textColor="Green", $lineColor="$ARROW_COLOR")
AddRelTag("share", $textColor="orange", $lineColor="grey", $lineStyle = DashedLine())


System_Boundary(repository_service_tuf_worker, "Repository Service for TUF Worker") #APPLICATION {
    Container_Boundary(app, "app.py") #DeepSkyBlue{
        Container(metadata_repository, "MetadataRepository", "File System")

    }

    Container_Boundary(rstuf_worker, "repository_service_tuf_worker")  #LightSkyBlue {
        Container_Ext(celery, "Celery()")
        Container_Ext(dynaconf, "Dynaconf()")
        Container_Ext(sqlalchemy, "SQLAlchemy()")
        Container(repository, "repository")

        Container_Boundary(models, "models") #PowderBlue {
            Container(TargetsFiles, "TargetFiles")
        }
        Container_Boundary(interfaces, "interfaces") #CornflowerBlue {
            Container(IStorage, "Storage Interface")
        }
        Container_Boundary(services, "services") #DeepSkyBlue{
            Container_Boundary(storage, "storage") #LightSteelBlue {
                Container(LocalStorage, "LocalStorage", "File System")
            }
        }
    }

    ContainerDb(data_dir, "/data", "File System", "Service settings\nRepository settings\nUsers db", $tags="storage_service")
}

Container_Ext(broker, "Broker", "RabbitMQ, Redis, etc", $tags="queue") #Grey
Container_Ext(redis, "Redis", "Redis Server", $tags="queue") #Grey
Container_Ext(sql, "SQL", "SQL Server", $tags="sql_db") #Grey
Container_Ext(ext_storage, "Metadata Storage", "specific technology", $tags="key_service") #Grey

Rel(LocalStorage, ext_storage, " ")
Rel(IStorage, LocalStorage, "uses")
Rel(metadata_repository, IStorage, " ")
Rel(metadata_repository, celery, " ")
Rel(metadata_repository, repository, " ")
Rel(metadata_repository, dynaconf, " ")
Rel(metadata_repository, models, " ")
Rel(models, sqlalchemy, " ")
Rel_U(dynaconf, redis, "Write data")
Rel(sqlalchemy, sql, "Write data")
Rel_D(celery, broker, "Publisher")
Rel(broker, celery, "Consumer")
Rel_L(dynaconf, data_dir, "Write data")


HIDE_STEREOTYPE()
@enduml