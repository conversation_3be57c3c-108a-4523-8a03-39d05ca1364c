"""initial db

Revision ID: 1effe827d0cd
Revises:
Create Date: 2025-02-12 15:49:21.927962

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "1effe827d0cd"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "rstuf_target_roles",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("rolename", sa.String(), nullable=False),
        sa.Column("expires", sa.DateTime(), nullable=False),
        sa.Column("version", sa.Integer(), nullable=False),
        sa.<PERSON>umn("active", sa.<PERSON>an(), nullable=False),
        sa.Column("last_update", sa.DateTime(), nullable=True),
        sa.Primary<PERSON>eyConstraint("id"),
    )
    op.create_index(
        op.f("ix_rstuf_target_roles_id"),
        "rstuf_target_roles",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_rstuf_target_roles_rolename"),
        "rstuf_target_roles",
        ["rolename"],
        unique=True,
    )
    op.create_table(
        "rstuf_target_files",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("path", sa.String(), nullable=False),
        sa.Column(
            "info", postgresql.JSON(astext_type=sa.Text()), nullable=False
        ),
        sa.Column("published", sa.Boolean(), nullable=False),
        sa.Column(
            "action",
            sa.Enum("ADD", "REMOVE", name="targetaction"),
            nullable=False,
        ),
        sa.Column("last_update", sa.DateTime(), nullable=True),
        sa.Column("targets_role", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["targets_role"],
            ["rstuf_target_roles.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_rstuf_target_files_id"),
        "rstuf_target_files",
        ["id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_rstuf_target_files_path"),
        "rstuf_target_files",
        ["path"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_rstuf_target_files_path"), table_name="rstuf_target_files"
    )
    op.drop_index(
        op.f("ix_rstuf_target_files_id"), table_name="rstuf_target_files"
    )
    op.drop_table("rstuf_target_files")
    op.drop_index(
        op.f("ix_rstuf_target_roles_rolename"), table_name="rstuf_target_roles"
    )
    op.drop_index(
        op.f("ix_rstuf_target_roles_id"), table_name="rstuf_target_roles"
    )
    op.drop_table("rstuf_target_roles")
    # ### end Alembic commands ###
