[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
watchdog = "*"
redis = "*"
tuf = "*"
dynaconf = {extras = ["ini"], version = "*"}
supervisor = "*"
securesystemslib = "*"
sqlalchemy = "*"
psycopg2 = "*"
alembic = "*"
pydantic = "*"
celery = "*"
boto3 = "*"
awswrangler = "*"
sigstore = "*"
pymysql = "*"
google-cloud-kms = "*"
rsa = "*"
awscli-local = "*"
bcdoc = "*"

[dev-packages]
tox = "*"
flake8 = "*"
coverage = "*"
black = "*"
isort = "*"
mypy = "*"
pytest = "*"
pytest-cov = "*"
pytest-xdist = "*"
pretend = "*"
virtualenv = "*"
sphinx-rtd-theme = "*"
sphinx = "*"
sphinxcontrib-applehelp = "*"
sphinxcontrib-devhelp = "*"
sphinxcontrib-htmlhelp = "*"
sphinxcontrib-jsmath = "*"
sphinxcontrib-plantuml = "*"
sphinxcontrib-qthelp = "*"
sphinxcontrib-serializinghtml = "*"
pre-commit = "*"
bandit = "*"
myst-parser = "*"
dynaconf = {extras = ["ini"], version = "*"}  # required to build docs

[requires]
python_version = "3.13"
