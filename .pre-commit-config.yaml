repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
  - id: check-added-large-files
  - id: end-of-file-fixer
    files: 'repository_service_tuf_worker/'
  - id: trailing-whitespace
    files: 'repository_service_tuf_worker/'
  - id: check-yaml
    files: '.github/'
- repo: https://github.com/pycqa/flake8
  rev: '7.2.0'
  hooks:
  - id: flake8
    exclude: repository_service_tuf_worker/__init__.py|venv|.venv|setting.py|.git|.tox|dist|docs|/*lib/python*|/*egg|build|tools|alembic
- repo: https://github.com/PyCQA/isort
  rev: '6.0.1'
  hooks:
  - id: isort
    args: [-l79, --profile, black, --check, --diff]
- repo: https://github.com/psf/black
  rev: '25.1.0'
  hooks:
  - id: black
    args: [-l79, --check, --diff, .]
- repo: https://github.com/PyCQA/bandit
  rev: '1.8.3'
  hooks:
  - id: bandit
    args: ["-r", "tuf_repository_service_worker"]
    exclude: tests.
- repo: local
  hooks:
    - id: tox-precommit
      name: run pre-commit version check from tox
      description: Checks if `pre-commit autoupdate` is up-to-date
      entry: tox -e precommit
      language: system
      pass_filenames: false
      verbose: true