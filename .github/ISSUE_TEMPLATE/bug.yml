name: 🪲 Bug
description: File a bug report
title: "Bug: "
labels: ["bug", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  - type: textarea
    id: description
    attributes:
      label: What happened?
      description: Please provide a detailed description of the bug. Also tell us, what did you expect to happen?
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: What steps did you take?
      description: Please provide a clear and concise description steps that can be used to reproduce the problem.
    validations:
      required: false
  - type: textarea
    id: expectations
    attributes:
      label: What behavior did you expect?
      description: Please provide a description of what was expected.
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this bug, you agree to follow our [Code of Conduct](CODE_OF_CONDUCT.rst)
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
