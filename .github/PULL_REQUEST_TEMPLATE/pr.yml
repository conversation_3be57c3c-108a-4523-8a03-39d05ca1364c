name: 📦 Pull request
description: Create a new pull request
labels: ["pr"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this pull request!
  - type: textarea
    id: description
    attributes:
      label: What is the PR about?
      description: Please fill in the fields below to submit a pull request. The more information that is provided, the better.
    validations:
      required: true
  - type: textarea
    id: issue
    attributes:
      label: Fixes #
      description: Please reference the issue this PR fixes.
    validations:
      required: false
  - type: checkboxes
    attributes:
      label: Types of changes
      options:
        - label: Bug fix (non-breaking change which fixes an issue)
        - label: New feature (non-breaking change which adds functionality)
        - label: Breaking change (fix or feature that would cause existing functionality to not work as expected).
  - type: checkboxes
    attributes:
      label: Additional requirements
      options:
        - label: Tests have been added for the bug fix or new feature
        - label: Docs have been added for the bug fix or new feature
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this PR, you agree to follow our [Code of Conduct](CODE_OF_CONDUCT.rst)
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
