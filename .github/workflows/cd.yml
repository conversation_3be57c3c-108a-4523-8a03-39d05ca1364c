name: CD

on:
  push:
    tags:
      - v*
  workflow_dispatch:
    inputs:
      api_version:
        description: "API Version"
        default: "latest"
        type: string
        required: False
      cli_version:
        description: "CLI Version"
        default: "latest"
        type: string
        required: False

jobs:
  publish-container-image:
    uses: ./.github/workflows/publish_container.yml
    with:
      image_version: ${{ github.sha }}

  set-component-versions:
    needs: publish-container-image
    runs-on: ubuntu-latest
    outputs:
      api_version: ${{ steps.api_version.outputs.VERSION }}
      cli_version: ${{ steps.cli_version.outputs.VERSION }}

    steps:
      - id: api_version
        name: dynamic input worker version
        run: |
          if [[ "${{ inputs.api_version }}" == "" ]]; then echo "VERSION=latest" >> $GITHUB_OUTPUT; else echo "VERSION=${{ inputs.api_version }}" >> $GITHUB_OUTPUT;fi

      - id: cli_version
        name: dynamic input cli version
        run: |
          if [[ "${{ inputs.cli_version }}" == "" ]]; then echo "VERSION=latest" >> $GITHUB_OUTPUT; else echo "VERSION=${{ inputs.cli_version }}" >> $GITHUB_OUTPUT;fi

  functional-tests-local:
    needs: set-component-versions
    name: FT Deploy Local Services
    uses: ./.github/workflows/functional-tests.yml
    with:
      api_version: ${{ needs.set-component-versions.outputs.api_version }}
      cli_version: ${{ needs.set-component-versions.outputs.cli_version }}

  functional-tests-local-redis:
    needs: set-component-versions
    name: FT Deploy Local Services with Redis as Broker
    uses: ./.github/workflows/functional-tests.yml
    with:
      docker_compose: docker-compose-redis.yml
      api_version: ${{ needs.set-component-versions.outputs.api_version }}
      cli_version: ${{ needs.set-component-versions.outputs.cli_version }}

  functional-tests-aws:
    needs: set-component-versions
    name: FT Deploy AWS Services
    uses: ./.github/workflows/functional-tests.yml
    with:
      docker_compose: docker-compose-aws.yml
      api_version: ${{ needs.set-component-versions.outputs.api_version }}
      cli_version: ${{ needs.set-component-versions.outputs.cli_version }}

  # functional-tests-mysql:
  #   needs: set-component-versions
  #   name: FT Deploy MySQL Server
  #   uses: ./.github/workflows/functional-tests.yml
  #   with:
  #     docker_compose: docker-compose-mysql.yml
  #     api_version: ${{ needs.set-component-versions.outputs.api_version }}
  #     cli_version: ${{ needs.set-component-versions.outputs.cli_version }}

  prepare-rc:
    runs-on: ubuntu-latest
    needs: [functional-tests-local, functional-tests-local-redis, functional-tests-aws]
    outputs:
      release_id: ${{ steps.gh-release.outputs.id }}
    steps:
      - id: gh-release
        name: Publish GitHub release candiate
        uses: softprops/action-gh-release@da05d552573ad5aba039eaac05058a918a7bf631
        with:
          draft: true
          name: ${{ github.ref_name }}-rc
          tag_name: ${{ github.ref }}
          body: "Release waiting for review ${{ github.sha }}: [ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.sha }}](https://github.com/repository-service-tuf/repository-service-tuf-worker/pkgs/container/repository-service-tuf-worker)"

  release:
    runs-on: ubuntu-latest
    needs: prepare-rc
    environment: release

    steps:
    - name: Login to GitHub Container Registry
      uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Publish Docker Image on GitHub Registry
      run: |
        docker pull ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.sha }}
        docker tag ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.sha }} ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.ref_name }}
        docker tag ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.sha }} ghcr.io/repository-service-tuf/repository-service-tuf-worker:latest
        docker push ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.ref_name }}
        docker push ghcr.io/repository-service-tuf/repository-service-tuf-worker:latest

    - name: Publish GitHub Release
      uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea
      with:
        script: |
          await github.rest.repos.updateRelease({
            owner: context.repo.owner,
            repo: context.repo.repo,
            draft: false,
            release_id: '${{ needs.prepare-rc.outputs.release_id }}',
            name: '${{ github.ref_name }}',
            body: 'docker pull [ghcr.io/repository-service-tuf/repository-service-tuf-worker:${{ github.ref_name }}](https://github.com/repository-service-tuf/repository-service-tuf-worker/pkgs/container/repository-service-tuf-worker)'
          })
