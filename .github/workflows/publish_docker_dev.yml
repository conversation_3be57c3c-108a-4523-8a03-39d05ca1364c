name: Build and Publish (dev) Docker Image

on:
  push:
    branches:
      - 'main'
    paths:
      - 'repository_service_tuf_worker/**'
      - 'app.py'
      - 'entrypoint.sh'
      - 'supervisor.conf'
      - 'Dockerfile'
      - 'requirements.txt'
      - 'setup.py'
      - 'alembic.ini'
      - 'alembic/**'

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
    - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065
      with:
        python-version: '3.13'

    - name: Set up QEMU
      uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2

    - name: Login to GitHub Container Registry
      uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push
      uses: docker/build-push-action@1dc73863535b631f98b2378be8619f83b136f4a0
      with:
        context: .
        push: true
        platforms: linux/amd64,linux/arm64
        tags: |
            ghcr.io/repository-service-tuf/repository-service-tuf-worker:dev
        build-args: |
          RELEASE_VERSION=dev