name: Functional Tests

on:
  workflow_dispatch:
    inputs:
      docker_compose:
        description: "Docker Compose File"
        default: "docker-compose.yml"
        type: string
        required: False
      umbrella_branch:
        description: "Umbrella Branch (Functional Tests)"
        default: "main"
        type: string
        required: False
      api_version:
        description: "API Version"
        default: "latest"
        type: string
        required: False
      cli_version:
        description: "CLI Version"
        default: "latest"
        type: string
        required: False

  workflow_call:
    inputs:
      docker_compose:
        description: "Docker Compose File"
        default: "docker-compose.yml"
        type: string
        required: False
      umbrella_branch:
        description: "Umbrella Branch (Functional Tests)"
        default: "main"
        type: string
        required: False
      api_version:
        description: "API Version"
        default: "latest"
        type: string
        required: False
      cli_version:
        description: "CLI Version"
        default: "latest"
        type: string
        required: False

jobs:
  functional-das:
    name: "DAS"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        pytest-group: [ "1", "2", "3"]

    steps:
      - name: Checkout RSTUF Worker source code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Checkout RSTUF Umbrella (FT)
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
            repository: repository-service-tuf/repository-service-tuf
            path: rstuf-umbrella
            ref: ${{ inputs.umbrella_branch }}

      - name: Deploy RSTUF with Worker container from source code
        uses: isbang/compose-action@8be2d741e891ac9b8ac20825e6f3904149599925
        with:
          compose-file: ${{ inputs.docker_compose }}
        env:
          API_VERSION: ${{ inputs.api_version }}

      - name: Bootstrap/Setup RSTUF DAS and run Functional Tests
        run: |
          make ft-das CLI_VERSION=${{ inputs.cli_version }} PYTEST_GROUP=${{ matrix.pytest-group }} DC=${{ inputs.docker_compose }}

  functional-das-slow:
    name: "DAS Slow"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout RSTUF Worker source code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Checkout RSTUF Umbrella (FT)
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
            repository: repository-service-tuf/repository-service-tuf
            path: rstuf-umbrella
            ref: ${{ inputs.umbrella_branch }}

      - name: Deploy RSTUF with Worker container from source code
        uses: isbang/compose-action@8be2d741e891ac9b8ac20825e6f3904149599925
        with:
          compose-file: ${{ inputs.docker_compose }}
        env:
          API_VERSION: ${{ inputs.api_version }}

      - name: Bootstrap/Setup RSTUF DAS and run Functional Tests
        run: |
          make ft-das CLI_VERSION=${{ inputs.cli_version }} PYTEST_GROUP=none SLOW="yes" DC=${{ inputs.docker_compose }}

  functional-signed:
    name: "Signed"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        pytest-group: [ "1", "2", "3"]

    steps:
      - name: Checkout RSTUF Worker source code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Checkout RSTUF Umbrella (FT)
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          repository: repository-service-tuf/repository-service-tuf
          path: rstuf-umbrella
          ref: ${{ inputs.umbrella_branch }}

      - name: Deploy RSTUF with Worker container from source code
        uses: isbang/compose-action@8be2d741e891ac9b8ac20825e6f3904149599925
        with:
          compose-file: ${{ inputs.docker_compose }}
        env:
          API_VERSION: ${{ inputs.api_version }}

      - name: Bootstrap/Setup RSTUF full Signed and run Functional Tests
        run: |
          make ft-signed CLI_VERSION=${{ inputs.cli_version }} PYTEST_GROUP=${{ matrix.pytest-group }} DC=${{ inputs.docker_compose }}
