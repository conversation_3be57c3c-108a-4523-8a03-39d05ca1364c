name: Update pre-commit hooks
on:
  workflow_dispatch:
  schedule:
    # Run at 8:00 AM every day
    - cron: "0 8 * * *"
jobs:
  update-pre-commit-hooks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065
        with:
          python-version: "3.13"
      - name: Install prerequisites
        run: |
          pip install pre-commit tox
      - name: Update pre-commit hooks
        run: |
          pre-commit autoupdate
      - name: Check for pre-commit config file changes
        id: git_diff
        run: |
          echo "GIT_DIFF=$(git diff --exit-code 1> /dev/null; echo $?)" >> $GITHUB_OUTPUT
      - name: Run tests
        if: steps.git_diff.outputs.GIT_DIFF == 1
        # We want to make sure that the new updates do not affect the code
        run: |
          make tests
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "build: Update pre-commit hooks"
          branch: "rstuf-bot/update-pre-commit-hooks"
          delete-branch: true
          title: "build: Update pre-commit hooks"
          body: >
            The following PR updates the pre-commit hooks (`.pre-commit-config.yaml` file) using `pre-commit autoupdate`.
          labels: report, automated pr, pre-commit
