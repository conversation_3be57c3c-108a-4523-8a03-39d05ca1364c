name: <PERSON> and Lint

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - "main"

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-versions: [ "3.13" ]

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

    - name: Check if any local image is used in docker-compose.yml
      run: |
        if [[ "$(egrep -w 'image:\s+repository-service-tuf-api|image:\s+repository-service-tuf-worker' docker-compose.yml -c)" -ne "0" ]]; then echo "Local image has been used in docker-compose.yml" && exit 1; fi

    - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065
      with:
        python-version: ${{ matrix.python-versions }}

    - name: Install tox and coverage
      run: pip install tox tox-gh-actions

    - name: Run Python tests
      run: tox

    - name: Codecov
      uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
      with:
        files: coverage.xml
        fail_ci_if_error: false
        verbose: true

